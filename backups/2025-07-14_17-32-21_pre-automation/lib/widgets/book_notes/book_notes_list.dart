import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/enums/sync_direction.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/providers/bookmark.dart';
import 'package:dasso_reader/service/book.dart';
import 'package:dasso_reader/utils/time_to_human.dart';
import 'package:dasso_reader/widgets/delete_confirm.dart';
import 'package:dasso_reader/widgets/tips/notes_tips.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:dasso_reader/config/design_system.dart';

// Constants for note types and colors (moved from removed excerpt_menu.dart)
const List<String> notesColors = [
  '66CCFF',
  'FF0000',
  '00FF00',
  'EB3BFF',
  'FFD700',
];
final List<Map<String, dynamic>> notesType = [
  {'type': 'highlight', 'icon': AdaptiveIcons.brush},
  {'type': 'underline', 'icon': AdaptiveIcons.formatUnderline},
];

class BookNotesList extends ConsumerStatefulWidget {
  const BookNotesList({
    super.key,
    required this.book,
    required this.reading,
    this.exportNotes,
    this.textColor,
    this.hideBookmarks = false,
  });

  final Book book;
  final bool reading;
  final void Function(BuildContext context, Book book, {List<BookNote>? notes})?
      exportNotes;
  final Color? textColor;
  final bool hideBookmarks;

  @override
  ConsumerState<BookNotesList> createState() => _BookNotesListState();
}

class _BookNotesListState extends ConsumerState<BookNotesList> {
  List<BookNote> bookNotes = [];
  List<BookNote> showNotes = [];
  List<BookNote> selectedNotes = [];
  String sortType = 'cfi';
  bool asc = true;
  List<bool> typeColorSelected =
      List.filled(notesType.length * notesColors.length, true);
  bool showBookMarks = true;

  @override
  void initState() {
    super.initState();
    _loadBookNotes();
  }

  @override
  void didUpdateWidget(covariant BookNotesList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.book.id != widget.book.id) {
      _loadBookNotes();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the bookmark provider to refresh notes when bookmarks change (only if bookmarks are not hidden)
    if (!widget.hideBookmarks) {
      ref.listen(BookmarkProvider(widget.book.id), (previous, next) {
        // Reload notes whenever bookmark state changes
        _loadBookNotes();
      });
    }

    return Column(
      children: [
        StickyHeader(
          header: header(context),
          content: showNotes.isEmpty
              ? const Column(
                  children: [
                    Divider(),
                    NotesTips(),
                  ],
                )
              : Column(
                  children: showNotes.map((bookNote) {
                    return bookNoteItem(context, bookNote, false);
                  }).toList(),
                ),
        ),
      ],
    );
  }

  Future<void> _loadBookNotes() async {
    List<BookNote> notes;
    if (widget.hideBookmarks) {
      // Load only actual notes (exclude bookmarks completely)
      notes = await selectBookNotesByBookIdExcludingBookmarks(widget.book.id);
    } else {
      // Load all notes including bookmarks
      notes = await selectBookNotesByBookId(widget.book.id);
    }
    setState(() {
      bookNotes = notes;
      // Apply filtering immediately to ensure consistent behavior
      _sortAndFilter();
    });
  }

  void _sortAndFilter() {
    List<BookNote> filterNotes = [];

    for (int i = 0; i < bookNotes.length; i++) {
      try {
        Map<String, dynamic> typeMap = notesType
            .firstWhere((element) => element['type'] == bookNotes[i].type);
        String color = bookNotes[i].color.toUpperCase();
        int index = notesType.indexOf(typeMap) * notesColors.length +
            notesColors.indexOf(color);

        if (typeColorSelected[index]) {
          filterNotes.add(bookNotes[i]);
        }
      } catch (e) {
        // Skip notes with types not in notesType (like bookmarks)
        continue;
      }
    }

    // Add bookmarks separately if showBookMarks is true and not hidden
    if (showBookMarks && !widget.hideBookmarks) {
      bookNotes.where((note) => note.type == 'bookmark').forEach((note) {
        filterNotes.add(note);
      });
    }

    // Sort the filtered notes
    if (sortType == 'time') {
      filterNotes.sort((a, b) {
        if (asc) {
          return a.createTime!.compareTo(b.createTime!);
        } else {
          return b.createTime!.compareTo(a.createTime!);
        }
      });
    } else {
      filterNotes.sort((a, b) {
        if (asc) {
          return _epubCfiCompare(a.cfi, b.cfi);
        } else {
          return _epubCfiCompare(b.cfi, a.cfi);
        }
      });
    }
    showNotes = filterNotes;
  }

  int _epubCfiCompare(String a, String b) {
    List<String> replace(String str) {
      return str
          .replaceAll('epubcfi(/', '')
          .replaceAll(')', '')
          .replaceAll(',', '')
          .split('/');
    }

    List<String> componentsA = replace(a);
    List<String> componentsB = replace(b);

    for (int i = 0; i < componentsA.length && i < componentsB.length; i++) {
      String compA = componentsA[i];
      String compB = componentsB[i];

      if (compA.isEmpty || compB.isEmpty) {
        continue;
      }
      if (compA != compB) {
        if (compA.contains(':') && compB.contains(':')) {
          int locA = int.tryParse(compA.split(':')[1]) ?? 0;
          int locB = int.tryParse(compB.split(':')[1]) ?? 0;
          return locA.compareTo(locB);
        } else {
          int numA = int.tryParse(compA.replaceAll('!', '')) ?? 0;
          int numB = int.tryParse(compB.replaceAll('!', '')) ?? 0;
          return numA.compareTo(numB);
        }
      }
    }

    return componentsA.length.compareTo(componentsB.length);
  }

  Widget bookNoteItem(BuildContext context, BookNote bookNote, bool selected) {
    Color iconColor = Color(int.parse('0xaa${bookNote.color}'));
    TextStyle infoStyle = TextStyle(
      fontSize: DesignSystem.fontSizeM,
      color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
    );

    Widget icon() {
      try {
        return Icon(
          notesType.firstWhere(
            (element) => element['type'] == bookNote.type,
          )['icon'] as IconData,
          color: iconColor,
        );
      } catch (e) {
        // Handle bookmarks and other unknown types
        return Icon(
          AdaptiveIcons.bookmark,
          color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
        );
      }
    }

    return GestureDetector(
      onTap: () {
        if (selectedNotes.isNotEmpty) {
          setState(() {
            if (selectedNotes.contains(bookNote)) {
              selectedNotes.remove(bookNote);
            } else {
              selectedNotes.add(bookNote);
            }
          });
        } else {
          if (widget.reading) {
            epubPlayerKey.currentState!.goToCfi(bookNote.cfi);
          } else {
            pushToReadingPage(ref, context, widget.book, cfi: bookNote.cfi);
          }
        }
      },
      onLongPress: () {
        setState(() {
          if (selectedNotes.contains(bookNote)) {
            selectedNotes.remove(bookNote);
          } else {
            selectedNotes.add(bookNote);
          }
        });
      },
      child: Card(
        shadowColor: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.all(DesignSystem.spaceS),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                child: icon(),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bookNote.content,
                      style: TextStyle(
                        fontSize: DesignSystem.fontSizeM,
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: true,
                        ),
                      ),
                    ),
                    if (bookNote.readerNote != null &&
                        bookNote.readerNote!.isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: DesignSystem.spaceXS),
                          IntrinsicHeight(
                            child: Row(
                              children: [
                                const VerticalDivider(
                                  thickness: 3,
                                ),
                                Expanded(
                                  child: Text(
                                    bookNote.readerNote!,
                                    style: infoStyle.copyWith(
                                      color: DesignSystem.getSettingsTextColor(
                                        context,
                                        isPrimary: false,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: DesignSystem.spaceXS),
                        ],
                      ),
                    Divider(
                      indent: 4,
                      height: 3,
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            bookNote.chapter,
                            style: infoStyle,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          timeToHuman(bookNote.createTime, context),
                          style: infoStyle,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (selectedNotes.isNotEmpty)
                IconButton(
                  onPressed: () {
                    setState(() {
                      if (selectedNotes.contains(bookNote)) {
                        selectedNotes.remove(bookNote);
                      } else {
                        selectedNotes.add(bookNote);
                      }
                    });
                  },
                  tooltip: selectedNotes.contains(bookNote)
                      ? 'Deselect note'
                      : 'Select note',
                  icon: Icon(
                    selectedNotes.contains(bookNote)
                        ? AdaptiveIcons.checkCircle
                        : AdaptiveIcons.circleOutlined,
                    color: Theme.of(context).colorScheme.primary,
                    semanticLabel: selectedNotes.contains(bookNote)
                        ? 'Note selected'
                        : 'Note not selected',
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget filterButton(BuildContext context) {
    Widget sortButton(
      BuildContext context,
      StateSetter sheetState,
      String text,
      String type,
    ) {
      return Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceS),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: sortType == type
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.surface,
            foregroundColor: sortType == type
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurface,
          ),
          onPressed: () {
            setState(() {
              sheetState(() {
                sortType = type;
                asc = !asc;
                _sortAndFilter();
              });
            });
          },
          child: Row(
            children: [
              Text(text),
              if (sortType == type)
                Icon(
                  asc ? AdaptiveIcons.arrowUpward : AdaptiveIcons.arrowDownward,
                ),
            ],
          ),
        ),
      );
    }

    Widget filterButton(
      BuildContext context,
      StateSetter sheetState,
      IconData icon,
      int typeIndex,
    ) {
      Widget colorButton(Color color, int index) => IconButton(
            onPressed: () {
              setState(() {
                sheetState(() {
                  typeColorSelected[index] = !typeColorSelected[index];
                  _sortAndFilter();
                });
              });
            },
            icon: Icon(
              typeColorSelected[index]
                  ? AdaptiveIcons.checkCircle
                  : AdaptiveIcons.circle,
              color: color,
            ),
            iconSize: 35,
          );

      return Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceS),
        child: Row(
          children: [
            IconButton(
              onPressed: () {
                setState(() {
                  sheetState(() {
                    for (int i = typeIndex * notesColors.length;
                        i < (typeIndex + 1) * notesColors.length;
                        i++) {
                      typeColorSelected[i] = !typeColorSelected[i];
                    }

                    _sortAndFilter();
                  });
                });
              },
              icon: Icon(icon),
            ),
            const Spacer(),
            ...notesColors.map((color) {
              return colorButton(
                Color(int.parse('0x99$color')),
                typeIndex * notesColors.length + notesColors.indexOf(color),
              );
            }),
          ],
        ),
      );
    }

    void showFilterBottomSheet() {
      showModalBottomSheet<void>(
        context: context,
        builder: (BuildContext context) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 10),
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter sheetState) => Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      sortButton(
                        context,
                        sheetState,
                        L10n.of(context).notes_page_sort_time,
                        'time',
                      ),
                      sortButton(
                        context,
                        sheetState,
                        L10n.of(context).notes_page_sort_chapter,
                        'cfi',
                      ),
                      const Spacer(),
                    ],
                  ),
                  if (!widget.hideBookmarks)
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              setState(() {
                                sheetState(() {
                                  showBookMarks = !showBookMarks;
                                  _sortAndFilter();
                                });
                              });
                            },
                            icon: Icon(
                              showBookMarks
                                  ? AdaptiveIcons.bookmarkFilled
                                  : AdaptiveIcons.bookmark,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            label:
                                Text(L10n.of(context).note_list_show_bookmark),
                          ),
                        ),
                      ],
                    ),
                  for (int i = 0; i < notesType.length; i++)
                    filterButton(
                      context,
                      sheetState,
                      notesType[i]['icon'] as IconData,
                      i,
                    ),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            sheetState(() {
                              typeColorSelected =
                                  List.filled(typeColorSelected.length, true);
                              _sortAndFilter();
                            });
                          });
                        },
                        child: Text(L10n.of(context).notes_page_filter_reset),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            foregroundColor:
                                Theme.of(context).colorScheme.onPrimary,
                          ),
                          onPressed: Navigator.of(context).pop,
                          child: Text(
                            L10n.of(context).notes_page_view_all_n_notes(
                              showNotes.length,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      );
    }

    return IconButton(
      onPressed: showFilterBottomSheet,
      icon: showNotes.length == bookNotes.length
          ? Icon(AdaptiveIcons.filterList)
          : Icon(AdaptiveIcons.filterListOff),
    );
  }

  Widget header(BuildContext context) {
    Color buttonColor = Theme.of(context).colorScheme.primary;
    List<Widget> filter = [
      const Spacer(),
      filterButton(context),
    ];
    List<Widget> selected = [
      IconButton(
        onPressed: () {
          setState(() {
            if (selectedNotes.length == showNotes.length) {
              selectedNotes.clear();
            } else {
              selectedNotes = List.from(showNotes);
            }
          });
        },
        tooltip: selectedNotes.length == showNotes.length
            ? 'Deselect all notes'
            : 'Select all notes',
        icon: Icon(
          selectedNotes.length == showNotes.length
              ? AdaptiveIcons.checkCircle
              : AdaptiveIcons.circleOutlined,
          color: Theme.of(context).colorScheme.primary,
          semanticLabel: selectedNotes.length == showNotes.length
              ? 'All notes selected'
              : 'No notes selected',
        ),
      ),
      const Spacer(),
      DeleteConfirm(
        delete: () {
          for (int i = 0; i < selectedNotes.length; i++) {
            deleteBookNoteById(selectedNotes[i].id!);
          }
          AnxWebdav().syncData(SyncDirection.upload, ref);
          setState(() {
            selectedNotes.clear();
            _loadBookNotes();
          });
        },
        deleteIcon: Icon(
          AdaptiveIcons.delete,
          color: buttonColor,
          semanticLabel: 'Delete selected notes',
        ),
        confirmIcon: Icon(
          AdaptiveIcons.close,
          color: Theme.of(context).colorScheme.error,
          semanticLabel: 'Cancel delete',
        ),
      ),
      if (!widget.reading)
        IconButton(
          onPressed: () {
            widget.exportNotes!(context, widget.book, notes: selectedNotes);
          },
          icon: Icon(
            AdaptiveIcons.iosShare,
            color: buttonColor,
          ),
        ),
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: selectedNotes.isNotEmpty ? selected : filter,
    );
  }
}
