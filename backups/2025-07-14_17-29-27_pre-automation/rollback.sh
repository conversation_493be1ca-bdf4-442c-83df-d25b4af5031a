#!/bin/bash
# DassoShu Reader - Rollback Design System Automation
# Generated: 2025-07-14 17:29:42.436268

cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-29-27_pre-automation/lib/page/home_page/hsk_page/hsk_learn_screen.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/page/home_page/hsk_page/hsk_learn_screen.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-29-27_pre-automation/lib/widgets/paste_text_fullscreen.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/paste_text_fullscreen.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-29-27_pre-automation/lib/widgets/common/orientation_aware_widgets.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/common/orientation_aware_widgets.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-29-27_pre-automation/lib/widgets/dictionary/context_menu_character_tab.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/dictionary/context_menu_character_tab.dart'

echo 'Rollback completed successfully!'
