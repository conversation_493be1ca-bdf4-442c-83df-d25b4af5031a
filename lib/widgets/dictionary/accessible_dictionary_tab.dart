import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/page/dictionary_page.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import 'package:stroke_order_animator/stroke_order_animator.dart';
import 'package:http/http.dart' as http;
import 'package:pinyin/pinyin.dart';

/// Widget for displaying dictionary lookup results in the context menu
/// with improved accessibility and internationalization
class AccessibleDictionaryTab extends StatefulWidget {
  /// The text to look up in the dictionary
  final String text;

  /// Whether to show the tab bar within the dictionary tab
  final bool showTabBar;

  /// The initial tab index to display
  final int initialTabIndex;

  /// Whether to limit the number of definitions displayed
  final bool limitDefinitions;

  /// Whether to enable batch character learning mode
  final bool batchCharacterMode;

  /// Whether to use compact mode for context menu display
  final bool compactMode;

  /// Maximum height constraint for the dictionary display
  final double? maxHeight;

  /// Whether to show stroke order animations
  final bool showStrokeAnimations;

  /// Whether to show only stroke animations without dictionary definitions
  final bool strokeOnlyMode;

  /// Optional override for maximum definitions count
  final int? maxDefinitionsOverride;

  const AccessibleDictionaryTab({
    super.key,
    required this.text,
    this.showTabBar = true,
    this.initialTabIndex = 0,
    this.limitDefinitions = true,
    this.batchCharacterMode = false,
    this.compactMode = false,
    this.maxHeight,
    this.showStrokeAnimations = true,
    this.strokeOnlyMode = false,
    this.maxDefinitionsOverride,
  });

  @override
  State<AccessibleDictionaryTab> createState() =>
      _AccessibleDictionaryTabState();
}

class _AccessibleDictionaryTabState extends State<AccessibleDictionaryTab>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  bool _hasError = false;
  DictionaryEntry? _entry;

  // For batch character mode
  List<String> _individualCharacters = [];
  int _currentCharacterIndex = 0;
  final Map<String, StrokeOrderAnimationController?> _strokeControllers = {};
  final Map<String, bool> _hasStrokeDataMap = {};
  final Map<String, bool> _loadingStrokeDataMap = {};

  final AudioPlayer _audioPlayer = AudioPlayer();
  final OnlineDictionaryService _onlineDictionaryService =
      OnlineDictionaryService();
  final DictionaryService _dictionaryService = DictionaryService();

  // Stroke order animator controller for managing animations
  StrokeOrderAnimationController? _strokeController;
  bool _hasStrokeData = false;
  bool _loadingStrokeData = false;
  final http.Client _httpClient = http.Client();

  // New fields for user preferences
  bool _showStrokeAnimations = true;
  int? _limitDefinitionsCount;

  @override
  void initState() {
    super.initState();
    // Load user preferences
    final showStrokes = Prefs().showStrokeAnimation;
    final maxDefs = Prefs().maxDefinitions;

    // Apply preferences unless explicitly overridden by parameters
    // In stroke-only mode, always show stroke animations
    _showStrokeAnimations =
        widget.strokeOnlyMode || (widget.showStrokeAnimations && showStrokes);

    // Use maxDefinitionsOverride if provided, otherwise use preferences
    _limitDefinitionsCount = widget.maxDefinitionsOverride ??
        (widget.limitDefinitions ? maxDefs : 5); // Default to 5 if not limited

    if (widget.batchCharacterMode) {
      _setupBatchMode();
    }

    _lookupWord();
  }

  /// Set up batch character mode by splitting the text into individual characters
  void _setupBatchMode() {
    if (widget.text.isEmpty) return;

    // Split text into individual Chinese characters
    _individualCharacters = widget.text
        .split('')
        .where(_isChinese) // Only include Chinese characters
        .toList();

    // Initialize stroke data maps
    for (var char in _individualCharacters) {
      _hasStrokeDataMap[char] = false;
      _loadingStrokeDataMap[char] = false;
    }

    // Reset current index
    _currentCharacterIndex = 0;
  }

  @override
  void dispose() {
    _audioPlayer.dispose();

    // Dispose the stroke controller only if it's not null
    if (_strokeController != null) {
      _strokeController!.dispose();
      _strokeController = null;
    }

    // Dispose all controllers in batch mode
    for (var entry in _strokeControllers.entries) {
      if (entry.value != null) {
        try {
          // Check if this controller is different from _strokeController
          // to avoid disposing the same controller twice
          if (_strokeController != entry.value) {
            entry.value!.dispose();
          }
        } catch (e) {
          AnxLog.severe('Error disposing stroke controller: $e');
        }
      }
    }
    _strokeControllers.clear();

    _httpClient.close();
    super.dispose();
  }

  @override
  void didUpdateWidget(AccessibleDictionaryTab oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.text != widget.text ||
        oldWidget.batchCharacterMode != widget.batchCharacterMode) {
      if (widget.batchCharacterMode) {
        _setupBatchMode();
      }

      _lookupWord();
    }
  }

  /// Look up the word in the dictionary
  Future<void> _lookupWord() async {
    if (widget.text.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Initialize the dictionary service if needed
      await DictionaryService().initialize();

      // Determine if the text is Chinese or English
      final isChinese = _isChinese(widget.text);

      if (isChinese) {
        // If in batch mode and we have individual characters
        if (widget.batchCharacterMode && _individualCharacters.isNotEmpty) {
          // Load the first character
          await _loadCharacterData(
            _individualCharacters[_currentCharacterIndex],
          );
        } else {
          // Regular mode - look up directly in CC-CEDICT dictionary
          final entry = await DictionaryService().lookupChinese(widget.text);

          if (entry == null) {
            // Try to create a fallback entry for single characters
            if (widget.text.length == 1) {
              final fallbackEntry = _createFallbackEntry(widget.text);
              setState(() {
                _entry = fallbackEntry;
                _isLoading = false;
              });

              // Try to load stroke order data for single character
              if (widget.text.length == 1) {
                _loadStrokeOrderData(widget.text);
              }

              return;
            }

            setState(() {
              _isLoading = false;
              _hasError = true;
            });
            return;
          }

          setState(() {
            _entry = entry;
            _isLoading = false;
          });

          // Try to load stroke order data for single character
          if (widget.text.length == 1) {
            _loadStrokeOrderData(widget.text);
          }
        }
      } else {
        // For English text, we'll show a list of Chinese words
        // For simplicity, we'll just show a message for now
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// Load data for a specific character in batch mode
  Future<void> _loadCharacterData(String character) async {
    if (character.isEmpty || !_isChinese(character)) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Look up the character
      final entry = await DictionaryService().lookupChinese(character);

      final characterEntry = entry ?? _createFallbackEntry(character);

      setState(() {
        _entry = characterEntry;
        _isLoading = false;
      });

      // Load stroke order data if not already loaded
      if (!_hasStrokeDataMap.containsKey(character) ||
          !_hasStrokeDataMap[character]!) {
        _loadBatchStrokeOrderData(character);
      } else {
        // If stroke data already loaded, just set the current controller
        setState(() {
          _strokeController = _strokeControllers[character];
          _hasStrokeData = _hasStrokeDataMap[character]!;
          _loadingStrokeData = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// Create a fallback entry for characters not in the database
  DictionaryEntry _createFallbackEntry(String character) {
    // Generate pinyin for the character
    String pinyinText = 'Unknown';
    try {
      if (_isChinese(character)) {
        pinyinText = PinyinHelper.getPinyin(
          character,
          separator: ' ',
          format: PinyinFormat.WITH_TONE_MARK,
        );
      }
    } catch (e) {
      AnxLog.warning('Error generating pinyin for fallback entry: $e');
    }

    return DictionaryEntry(
      traditional: character,
      simplified: character,
      pinyin: pinyinText,
      definitions: ['No definition available'],
      hskLevel: 0, // Use 0 to indicate HSK N/A consistently
      frequency: null,
    );
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// Play audio for the word
  Future<void> _playAudio() async {
    if (_entry == null) return;

    try {
      final word = _entry!.simplified;

      // Use the online dictionary service to play pronunciation
      // Note: Removed loading state management to match unified context menu behavior
      // and eliminate UI refresh artifacts during pronunciation playback
      await _onlineDictionaryService.playPronunciation(word, context: context);
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing audio: $e'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Load stroke order data for a character
  Future<void> _loadStrokeOrderData(String character) async {
    // Skip loading stroke data if animations are disabled
    if (!_showStrokeAnimations) {
      return;
    }

    if (character.length != 1) return;

    setState(() {
      _loadingStrokeData = true;
      _hasStrokeData = false;
    });

    try {
      final strokeOrderData = await downloadStrokeOrder(character, _httpClient);
      final controller = StrokeOrderAnimationController(
        StrokeOrder(strokeOrderData),
        this,
      );

      if (mounted) {
        setState(() {
          _strokeController = controller;
          _hasStrokeData = true;
          _loadingStrokeData = false;
        });
      }
    } catch (e) {
      AnxLog.severe('Error loading stroke order data: $e');
      if (mounted) {
        setState(() {
          _hasStrokeData = false;
          _loadingStrokeData = false;
        });
      }
    }
  }

  /// Load stroke order data for a character in batch mode
  Future<void> _loadBatchStrokeOrderData(String character) async {
    if (character.length != 1) return;

    setState(() {
      _loadingStrokeDataMap[character] = true;
      _hasStrokeDataMap[character] = false;
      _loadingStrokeData = true;
      _hasStrokeData = false;
    });

    try {
      final strokeOrderData = await downloadStrokeOrder(character, _httpClient);
      final controller = StrokeOrderAnimationController(
        StrokeOrder(strokeOrderData),
        this,
      );

      if (mounted) {
        setState(() {
          _strokeControllers[character] = controller;
          _hasStrokeDataMap[character] = true;
          _loadingStrokeDataMap[character] = false;

          // Set current controller
          _strokeController = controller;
          _hasStrokeData = true;
          _loadingStrokeData = false;
        });
      }
    } catch (e) {
      AnxLog.severe('Error loading stroke order data: $e');
      if (mounted) {
        setState(() {
          _hasStrokeDataMap[character] = false;
          _loadingStrokeDataMap[character] = false;
          _hasStrokeData = false;
          _loadingStrokeData = false;
        });
      }
    }
  }

  /// Move to the next character in batch mode
  void _nextCharacter() {
    if (!widget.batchCharacterMode || _individualCharacters.isEmpty) return;

    int nextIndex = (_currentCharacterIndex + 1) % _individualCharacters.length;
    setState(() {
      _currentCharacterIndex = nextIndex;
    });

    _loadCharacterData(_individualCharacters[nextIndex]);
  }

  /// Move to the previous character in batch mode
  void _previousCharacter() {
    if (!widget.batchCharacterMode || _individualCharacters.isEmpty) return;

    int prevIndex =
        (_currentCharacterIndex - 1 + _individualCharacters.length) %
            _individualCharacters.length;
    setState(() {
      _currentCharacterIndex = prevIndex;
    });

    _loadCharacterData(_individualCharacters[prevIndex]);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Apply max height constraint if provided
    Widget content = _buildContent(context);
    if (widget.maxHeight != null) {
      content = ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: widget.maxHeight!,
        ),
        child: SingleChildScrollView(
          child: content,
        ),
      );
    }

    // Apply compact styling for context menu if needed
    if (widget.compactMode) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceS),
        child: content,
      );
    }

    return content;
  }

  Widget _buildContent(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Instead of showing error messages, create a clean fallback view
    if (_hasError || _entry == null) {
      // Create a fallback entry if we have text to work with
      if (widget.text.isNotEmpty && _isChinese(widget.text)) {
        // Generate a fallback entry with proper pinyin
        _entry = _createFallbackEntry(widget.text);
        _hasError = false;

        // Continue to the normal dictionary display with our fallback entry
        // Handle stroke-only mode for fallback entries
        if (widget.strokeOnlyMode) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(DesignSystem.spaceM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Character display
                  Text(
                    _entry!.simplified,
                    style: Theme.of(context).textTheme.displayLarge?.copyWith(
                          fontSize: DesignSystem.fontSizeDisplayL,
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: DesignSystem.spaceM),
                  // Pinyin
                  Text(
                    _entry!.formattedPinyin(),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontStyle: FontStyle.italic,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: DesignSystem.spaceL),
                  // Stroke order animator (always shown in stroke-only mode)
                  _buildStrokeOrderAnimator(),
                ],
              ),
            ),
          );
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceS),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDictionaryHeader(),
                const Divider(),
                // Add stroke order animator only in batch mode to save space in main dictionary
                if (widget.batchCharacterMode && _entry!.isSingleCharacter)
                  _buildStrokeOrderAnimator(),
              ],
            ),
          ),
        );
      }

      // For non-Chinese text or empty text, show minimal feedback
      return Center(
        child: Icon(
          AdaptiveIcons.searchOff,
          size: DesignSystem.widgetIconSizeLarge,
          color: Theme.of(context)
              .colorScheme
              .onSurfaceVariant
              .withAlpha((0.5 * 255).round()),
        ),
      );
    }

    // Stroke-only mode: show only stroke animations without definitions
    if (widget.strokeOnlyMode) {
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(DesignSystem.spaceM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Character display
              Text(
                _entry!.simplified,
                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                      fontSize: DesignSystem.fontSizeDisplayL,
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: DesignSystem.spaceM),
              // Pinyin
              Text(
                _entry!.formattedPinyin(),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontStyle: FontStyle.italic,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: DesignSystem.spaceL),
              // Stroke order animator (always shown in stroke-only mode)
              _buildStrokeOrderAnimator(),
            ],
          ),
        ),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceS),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDictionaryHeader(),
            const Divider(),
            // Add batch navigation controls if in batch mode
            if (widget.batchCharacterMode && _individualCharacters.length > 1)
              _buildBatchNavigationControls(),
            // Add stroke order animator only in batch mode to save space in main dictionary
            // Main dictionary users can access stroke animations via the dedicated study page
            if (widget.batchCharacterMode) _buildStrokeOrderAnimator(),
            _buildDefinitions(),
          ],
        ),
      ),
    );
  }

  /// Build navigation controls for batch character mode
  Widget _buildBatchNavigationControls() {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceS),
      child: Column(
        children: [
          Text(
            'Character ${_currentCharacterIndex + 1} of ${_individualCharacters.length}',
            style: theme.textTheme.labelLarge,
          ),
          const SizedBox(height: DesignSystem.spaceS),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OutlinedButton.icon(
                icon: Icon(AdaptiveIcons.back),
                label: const Text('Previous'),
                onPressed: _previousCharacter,
              ),
              const SizedBox(width: DesignSystem.spaceM),
              OutlinedButton.icon(
                icon: Icon(AdaptiveIcons.forward),
                label: const Text('Next'),
                onPressed: _nextCharacter,
              ),
            ],
          ),
          const Divider(),
        ],
      ),
    );
  }

  /// Build the dictionary header
  Widget _buildDictionaryHeader() {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Word in simplified with HSK level badge if available
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Semantics(
                    label: 'Chinese word: ${_entry!.simplified}',
                    header: true,
                    child: Text(
                      _entry!.simplified,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_entry!.hskLevel != null) ...[
                    const SizedBox(width: DesignSystem.spaceS),
                    Semantics(
                      label: 'HSK Level ${_entry!.hskLevel}',
                      child: _buildHskLevelBadge(_entry!.hskLevel!),
                    ),
                  ],
                ],
              ),

              // Pinyin
              Semantics(
                label: 'Pronunciation: ${_entry!.formattedPinyin()}',
                child: Text(
                  _entry!.formattedPinyin(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Brush button (stroke animation practice) - first position
        if (_isChinese(_entry!.simplified))
          Semantics(
            button: true,
            label: 'Stroke Order Practice for ${_entry!.simplified}',
            child: IconButton(
              icon: Icon(AdaptiveIcons.brush),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (context) =>
                        BatchCharacterStudyPage(text: _entry!.simplified),
                  ),
                );
              },
              tooltip: 'Stroke Order Practice',
              focusColor: theme.colorScheme.primaryContainer,
              highlightColor:
                  theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            ),
          ),

        // Copy button - second position
        Semantics(
          button: true,
          label: 'Copy ${_entry!.simplified}',
          child: IconButton(
            icon: Icon(AdaptiveIcons.copy),
            onPressed: () {
              HapticFeedback.selectionClick();
              Clipboard.setData(ClipboardData(text: _entry!.simplified));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(L10n.of(context).notes_page_copied),
                  duration: const Duration(seconds: 1),
                ),
              );
            },
            tooltip: L10n.of(context).common_copy,
            focusColor: theme.colorScheme.primaryContainer,
            highlightColor:
                theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
          ),
        ),

        // Audio button (pronunciation) - third position
        Semantics(
          button: true,
          label: 'Pronunciation of ${_entry!.simplified}',
          child: IconButton(
            icon: Icon(AdaptiveIcons.volume),
            onPressed: _playAudio,
            tooltip: 'Pronunciation',
            focusColor: theme.colorScheme.primaryContainer,
            highlightColor:
                theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
          ),
        ),
      ],
    );
  }

  /// Build an HSK level badge
  Widget _buildHskLevelBadge(int level) {
    // Define colors for different HSK levels using theme colors
    final Map<int, Color> hskColors = {
      1: Theme.of(context).colorScheme.primary,
      2: Theme.of(context).colorScheme.secondary,
      3: Theme.of(context).colorScheme.tertiary,
      4: Theme.of(context).colorScheme.outline,
      5: Theme.of(context).colorScheme.onSurfaceVariant,
      6: Theme.of(context).colorScheme.error,
    };

    final color = hskColors[level] ?? Theme.of(context).colorScheme.outline;

    return Tooltip(
      message: 'HSK Level $level',
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceS,
            vertical: DesignSystem.spaceXS / 2),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.2),
          border: Border.all(color: color),
          borderRadius: BorderRadius.circular(DesignSystem.radiusML),
        ),
        child: Text(
          'HSK $level',
          style: TextStyle(
            fontSize: DesignSystem.fontSizeS,
            fontWeight: FontWeight.bold,
            color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
          ),
        ),
      ),
    );
  }

  /// Build the definitions with polyphonic character support
  Widget _buildDefinitions() {
    final theme = Theme.of(context);

    // For polyphonic character support, we need to get all entries for single characters in main dictionary
    if (!widget.compactMode && _entry!.isSingleCharacter) {
      return FutureBuilder<List<DictionaryEntry>>(
        future: _dictionaryService.lookupChineseAll(_entry!.simplified),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          List<DictionaryEntry> allEntries = snapshot.data ?? [_entry!];

          // Group entries by pronunciation for polyphonic character display
          Map<String, List<DictionaryEntry>> pronunciationGroups = {};
          for (var entry in allEntries) {
            String normalizedPinyin = entry.pinyin.toLowerCase().trim();
            if (!pronunciationGroups.containsKey(normalizedPinyin)) {
              pronunciationGroups[normalizedPinyin] = [];
            }
            pronunciationGroups[normalizedPinyin]!.add(entry);
          }

          // Check if this is a polyphonic character (multiple pronunciations)
          bool isPolyphonic = pronunciationGroups.length > 1;

          if (isPolyphonic) {
            // Display polyphonic character format
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: pronunciationGroups.entries.map((pronunciationGroup) {
                List<DictionaryEntry> entriesForPronunciation =
                    pronunciationGroup.value;
                DictionaryEntry firstEntry = entriesForPronunciation.first;

                return Padding(
                  padding: const EdgeInsets.only(bottom: DesignSystem.spaceM),
                  child: Container(
                    padding: const EdgeInsets.all(DesignSystem.spaceM),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest
                          .withAlpha((0.3 * 255).round()),
                      borderRadius:
                          BorderRadius.circular(DesignSystem.radiusML),
                      border: Border.all(
                        color: theme.colorScheme.outline
                            .withAlpha((0.2 * 255).round()),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Character display
                        SizedBox(
                          width: 80,
                          child: Column(
                            children: [
                              Text(
                                firstEntry.simplified,
                                style: theme.textTheme.displaySmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onSurface,
                                ),
                              ),
                              const SizedBox(height: DesignSystem.spaceS),
                              Text(
                                firstEntry.formattedPinyin(),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: DesignSystem.getSettingsTextColor(
                                    context,
                                    isPrimary: true,
                                  ),
                                  fontStyle: FontStyle.italic,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: DesignSystem.spaceL),

                        // Definitions in horizontal format
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Collect all definitions for this pronunciation
                              ...() {
                                List<String> allDefinitions = [];
                                for (var entry in entriesForPronunciation) {
                                  allDefinitions.addAll(entry.definitions);
                                }

                                return [
                                  Text(
                                    allDefinitions.join(' / '),
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      color: theme.colorScheme.onSurface,
                                      height: 1.4,
                                    ),
                                    softWrap: true,
                                  ),
                                ];
                              }(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            );
          } else {
            // Display horizontal format for non-polyphonic characters to save vertical space
            List<String> allDefinitions = [];
            for (var entry in allEntries) {
              allDefinitions.addAll(entry.definitions);
            }

            return Text(
              allDefinitions.join(' / '),
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface,
                height: 1.4,
              ),
              softWrap: true,
            );
          }
        },
      );
    } else {
      // For compact mode or non-single characters, use regular display
      return _buildRegularDefinitions();
    }
  }

  /// Build regular definitions display
  Widget _buildRegularDefinitions() {
    final theme = Theme.of(context);
    final definitions = _entry!.definitions;

    // If we have more definitions than the limit and limitDefinitions is true, limit them
    final displayDefinitions = _limitDefinitionsCount != null &&
            definitions.length > _limitDefinitionsCount!
        ? definitions.sublist(0, _limitDefinitionsCount!)
        : definitions;

    // For compact mode (context menu), show definitions in a horizontal inline format
    if (widget.compactMode) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceXS),
        child: RichText(
          text: TextSpan(
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            children: [
              for (int i = 0; i < displayDefinitions.length; i++) ...[
                TextSpan(
                  text: '${i + 1}. ${displayDefinitions[i]}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                if (i < displayDefinitions.length - 1)
                  TextSpan(
                    text: '  ',
                    style: theme.textTheme.bodyMedium,
                  ),
              ],
            ],
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      );
    }

    // Always display definitions horizontally to save vertical space
    return Text(
      displayDefinitions.join(' / '),
      style: theme.textTheme.bodyMedium?.copyWith(
        color: theme.colorScheme.onSurface,
        height: 1.4,
      ),
      maxLines: widget.compactMode ? 3 : null,
      overflow: widget.compactMode ? TextOverflow.ellipsis : null,
      softWrap: true,
    );
  }

  /// Build stroke order animator widget
  Widget _buildStrokeOrderAnimator() {
    // If stroke animations are disabled, don't show anything
    if (!_showStrokeAnimations) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    if (_loadingStrokeData) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceM),
          child: Column(
            children: [
              const SizedBox(height: DesignSystem.spaceS),
              const CircularProgressIndicator(),
              const SizedBox(height: DesignSystem.spaceS),
              Text(
                'Loading stroke order...',
                style: theme.textTheme.bodySmall,
              ),
            ],
          ),
        ),
      );
    }

    if (!_hasStrokeData || _strokeController == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              'Stroke Order',
              style: theme.textTheme.titleMedium,
            ),
          ),
          Center(
            child: SizedBox(
              height: 200,
              width: 200,
              child: StrokeOrderAnimator(
                _strokeController!,
                key: ValueKey(_entry!.simplified),
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(AdaptiveIcons.play),
                    onPressed: () {
                      if (_strokeController != null) {
                        _strokeController!.startAnimation();
                      }
                    },
                    tooltip: 'Play animation',
                  ),
                  IconButton(
                    icon: Icon(AdaptiveIcons.pause),
                    onPressed: () {
                      if (_strokeController != null) {
                        _strokeController!.stopAnimation();
                      }
                    },
                    tooltip: 'Pause animation',
                  ),
                  IconButton(
                    icon: Icon(AdaptiveIcons.stop),
                    onPressed: () {
                      if (_strokeController != null) {
                        _strokeController!.reset();
                      }
                    },
                    tooltip: 'Reset animation',
                  ),
                ],
              ),
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
