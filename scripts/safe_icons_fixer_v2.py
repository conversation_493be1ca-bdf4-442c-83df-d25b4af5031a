#!/usr/bin/env python3
"""
Safe Icons Fixer v2.0 - DassoShu Reader Cross-Platform Icons Automation
Applies semantic-first Material Design strategy to Icons violations with zero breaking changes.

Based on the proven Automation + Manual Hybrid Approach from safe_design_system_fixer_v2.py
Targets 177 Icons violations identified by cross-platform analyzer.

Safety Features:
- Automatic backup creation before any changes
- Comprehensive syntax validation after each change
- Automatic rollback on syntax errors
- Batch processing with progress tracking
- Zero breaking changes policy

Usage:
    python3 scripts/safe_icons_fixer_v2.py [--batch] [--dry-run] [--verbose]
"""

import os
import re
import sys
import shutil
import subprocess
import tempfile
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class SafeIconsFixer:
    def __init__(self, project_root: str, dry_run: bool = False, verbose: bool = False):
        self.project_root = Path(project_root)
        self.dry_run = dry_run
        self.verbose = verbose
        self.backup_dir = self.project_root / "backups" / f"icons_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.fixes_applied = 0
        self.files_processed = 0
        self.errors_encountered = 0
        
        # Icon conversion patterns - Semantic-first Material Design strategy
        self.icon_patterns = [
            # Basic Material Icons to AdaptiveIcons
            (r'\bIcons\.add\b', 'AdaptiveIcons.add'),
            (r'\bIcons\.settings\b', 'AdaptiveIcons.settings'),
            (r'\bIcons\.search\b', 'AdaptiveIcons.search'),
            (r'\bIcons\.arrow_back\b', 'AdaptiveIcons.back'),
            (r'\bIcons\.arrow_forward\b', 'AdaptiveIcons.forward'),
            (r'\bIcons\.close\b', 'AdaptiveIcons.close'),
            (r'\bIcons\.delete\b', 'AdaptiveIcons.delete'),
            (r'\bIcons\.edit\b', 'AdaptiveIcons.edit'),
            (r'\bIcons\.info\b', 'AdaptiveIcons.info'),
            (r'\bIcons\.info_outline\b', 'AdaptiveIcons.info'),
            (r'\bIcons\.help\b', 'AdaptiveIcons.help'),
            (r'\bIcons\.help_outline\b', 'AdaptiveIcons.help'),
            (r'\bIcons\.home\b', 'AdaptiveIcons.home'),
            (r'\bIcons\.menu\b', 'AdaptiveIcons.menu'),
            (r'\bIcons\.more_vert\b', 'AdaptiveIcons.moreVert'),
            (r'\bIcons\.more_horiz\b', 'AdaptiveIcons.moreHoriz'),
            (r'\bIcons\.share\b', 'AdaptiveIcons.share'),
            (r'\bIcons\.copy\b', 'AdaptiveIcons.copy'),
            (r'\bIcons\.content_copy\b', 'AdaptiveIcons.copy'),
            (r'\bIcons\.paste\b', 'AdaptiveIcons.paste'),
            (r'\bIcons\.cut\b', 'AdaptiveIcons.cut'),
            (r'\bIcons\.content_cut\b', 'AdaptiveIcons.cut'),
            (r'\bIcons\.save\b', 'AdaptiveIcons.save'),
            (r'\bIcons\.download\b', 'AdaptiveIcons.download'),
            (r'\bIcons\.upload\b', 'AdaptiveIcons.upload'),
            (r'\bIcons\.refresh\b', 'AdaptiveIcons.refresh'),
            (r'\bIcons\.sync\b', 'AdaptiveIcons.sync'),
            (r'\bIcons\.check\b', 'AdaptiveIcons.check'),
            (r'\bIcons\.check_circle\b', 'AdaptiveIcons.checkCircle'),
            (r'\bIcons\.cancel\b', 'AdaptiveIcons.cancel'),
            (r'\bIcons\.clear\b', 'AdaptiveIcons.clear'),
            (r'\bIcons\.visibility\b', 'AdaptiveIcons.visibility'),
            (r'\bIcons\.visibility_off\b', 'AdaptiveIcons.visibilityOff'),
            (r'\bIcons\.favorite\b', 'AdaptiveIcons.favorite'),
            (r'\bIcons\.favorite_border\b', 'AdaptiveIcons.favoriteBorder'),
            (r'\bIcons\.star\b', 'AdaptiveIcons.star'),
            (r'\bIcons\.star_border\b', 'AdaptiveIcons.starBorder'),
            (r'\bIcons\.bookmark\b', 'AdaptiveIcons.bookmark'),
            (r'\bIcons\.bookmark_border\b', 'AdaptiveIcons.bookmarkBorder'),
            (r'\bIcons\.play_arrow\b', 'AdaptiveIcons.play'),
            (r'\bIcons\.pause\b', 'AdaptiveIcons.pause'),
            (r'\bIcons\.stop\b', 'AdaptiveIcons.stop'),
            (r'\bIcons\.skip_next\b', 'AdaptiveIcons.skipNext'),
            (r'\bIcons\.skip_previous\b', 'AdaptiveIcons.skipPrevious'),
            (r'\bIcons\.volume_up\b', 'AdaptiveIcons.volumeUp'),
            (r'\bIcons\.volume_down\b', 'AdaptiveIcons.volumeDown'),
            (r'\bIcons\.volume_off\b', 'AdaptiveIcons.volumeOff'),
            
            # CupertinoIcons to AdaptiveIcons (semantic-first approach)
            (r'\bCupertinoIcons\.add\b', 'AdaptiveIcons.add'),
            (r'\bCupertinoIcons\.settings\b', 'AdaptiveIcons.settings'),
            (r'\bCupertinoIcons\.search\b', 'AdaptiveIcons.search'),
            (r'\bCupertinoIcons\.back\b', 'AdaptiveIcons.back'),
            (r'\bCupertinoIcons\.forward\b', 'AdaptiveIcons.forward'),
            (r'\bCupertinoIcons\.clear\b', 'AdaptiveIcons.close'),
            (r'\bCupertinoIcons\.delete\b', 'AdaptiveIcons.delete'),
            (r'\bCupertinoIcons\.pencil\b', 'AdaptiveIcons.edit'),
            (r'\bCupertinoIcons\.info\b', 'AdaptiveIcons.info'),
            (r'\bCupertinoIcons\.question\b', 'AdaptiveIcons.help'),
            (r'\bCupertinoIcons\.home\b', 'AdaptiveIcons.home'),
            (r'\bCupertinoIcons\.ellipsis\b', 'AdaptiveIcons.moreHoriz'),
            (r'\bCupertinoIcons\.share\b', 'AdaptiveIcons.share'),
            (r'\bCupertinoIcons\.doc_on_clipboard\b', 'AdaptiveIcons.copy'),
            (r'\bCupertinoIcons\.refresh\b', 'AdaptiveIcons.refresh'),
            (r'\bCupertinoIcons\.checkmark\b', 'AdaptiveIcons.check'),
            (r'\bCupertinoIcons\.checkmark_circle\b', 'AdaptiveIcons.checkCircle'),
            (r'\bCupertinoIcons\.xmark\b', 'AdaptiveIcons.cancel'),
            (r'\bCupertinoIcons\.eye\b', 'AdaptiveIcons.visibility'),
            (r'\bCupertinoIcons\.eye_slash\b', 'AdaptiveIcons.visibilityOff'),
            (r'\bCupertinoIcons\.heart\b', 'AdaptiveIcons.favorite'),
            (r'\bCupertinoIcons\.heart_fill\b', 'AdaptiveIcons.favorite'),
            (r'\bCupertinoIcons\.star\b', 'AdaptiveIcons.star'),
            (r'\bCupertinoIcons\.star_fill\b', 'AdaptiveIcons.star'),
            (r'\bCupertinoIcons\.bookmark\b', 'AdaptiveIcons.bookmark'),
            (r'\bCupertinoIcons\.bookmark_fill\b', 'AdaptiveIcons.bookmark'),
            (r'\bCupertinoIcons\.play\b', 'AdaptiveIcons.play'),
            (r'\bCupertinoIcons\.pause\b', 'AdaptiveIcons.pause'),
            (r'\bCupertinoIcons\.stop\b', 'AdaptiveIcons.stop'),

            # Additional common Material Icons patterns
            (r'\bIcons\.folder\b', 'AdaptiveIcons.folder'),
            (r'\bIcons\.folder_open\b', 'AdaptiveIcons.folderOpen'),
            (r'\bIcons\.file_copy\b', 'AdaptiveIcons.fileCopy'),
            (r'\bIcons\.file_download\b', 'AdaptiveIcons.fileDownload'),
            (r'\bIcons\.file_upload\b', 'AdaptiveIcons.fileUpload'),
            (r'\bIcons\.image\b', 'AdaptiveIcons.image'),
            (r'\bIcons\.photo\b', 'AdaptiveIcons.photo'),
            (r'\bIcons\.camera\b', 'AdaptiveIcons.camera'),
            (r'\bIcons\.video_call\b', 'AdaptiveIcons.videoCall'),
            (r'\bIcons\.mic\b', 'AdaptiveIcons.mic'),
            (r'\bIcons\.mic_off\b', 'AdaptiveIcons.micOff'),
            (r'\bIcons\.phone\b', 'AdaptiveIcons.phone'),
            (r'\bIcons\.email\b', 'AdaptiveIcons.email'),
            (r'\bIcons\.message\b', 'AdaptiveIcons.message'),
            (r'\bIcons\.chat\b', 'AdaptiveIcons.chat'),
            (r'\bIcons\.send\b', 'AdaptiveIcons.send'),
            (r'\bIcons\.reply\b', 'AdaptiveIcons.reply'),
            (r'\bIcons\.forward\b', 'AdaptiveIcons.forward'),
            (r'\bIcons\.undo\b', 'AdaptiveIcons.undo'),
            (r'\bIcons\.redo\b', 'AdaptiveIcons.redo'),
            (r'\bIcons\.sort\b', 'AdaptiveIcons.sort'),
            (r'\bIcons\.filter_list\b', 'AdaptiveIcons.filterList'),
            (r'\bIcons\.grid_view\b', 'AdaptiveIcons.gridView'),
            (r'\bIcons\.list\b', 'AdaptiveIcons.list'),
            (r'\bIcons\.view_list\b', 'AdaptiveIcons.viewList'),
            (r'\bIcons\.view_module\b', 'AdaptiveIcons.viewModule'),
            (r'\bIcons\.fullscreen\b', 'AdaptiveIcons.fullscreen'),
            (r'\bIcons\.fullscreen_exit\b', 'AdaptiveIcons.fullscreenExit'),
            (r'\bIcons\.zoom_in\b', 'AdaptiveIcons.zoomIn'),
            (r'\bIcons\.zoom_out\b', 'AdaptiveIcons.zoomOut'),
            (r'\bIcons\.rotate_left\b', 'AdaptiveIcons.rotateLeft'),
            (r'\bIcons\.rotate_right\b', 'AdaptiveIcons.rotateRight'),
            (r'\bIcons\.flip\b', 'AdaptiveIcons.flip'),
            (r'\bIcons\.crop\b', 'AdaptiveIcons.crop'),
            (r'\bIcons\.color_lens\b', 'AdaptiveIcons.colorLens'),
            (r'\bIcons\.palette\b', 'AdaptiveIcons.palette'),
            (r'\bIcons\.brush\b', 'AdaptiveIcons.brush'),
            (r'\bIcons\.format_paint\b', 'AdaptiveIcons.formatPaint'),
            (r'\bIcons\.text_fields\b', 'AdaptiveIcons.textFields'),
            (r'\bIcons\.format_size\b', 'AdaptiveIcons.formatSize'),
            (r'\bIcons\.format_bold\b', 'AdaptiveIcons.formatBold'),
            (r'\bIcons\.format_italic\b', 'AdaptiveIcons.formatItalic'),
            (r'\bIcons\.format_underlined\b', 'AdaptiveIcons.formatUnderlined'),
            (r'\bIcons\.format_color_text\b', 'AdaptiveIcons.formatColorText'),
            (r'\bIcons\.highlight\b', 'AdaptiveIcons.highlight'),
            (r'\bIcons\.translate\b', 'AdaptiveIcons.translate'),
            (r'\bIcons\.language\b', 'AdaptiveIcons.language'),
            (r'\bIcons\.public\b', 'AdaptiveIcons.public'),
            (r'\bIcons\.location_on\b', 'AdaptiveIcons.locationOn'),
            (r'\bIcons\.location_off\b', 'AdaptiveIcons.locationOff'),
            (r'\bIcons\.gps_fixed\b', 'AdaptiveIcons.gpsFixed'),
            (r'\bIcons\.navigation\b', 'AdaptiveIcons.navigation'),
            (r'\bIcons\.explore\b', 'AdaptiveIcons.explore'),
            (r'\bIcons\.map\b', 'AdaptiveIcons.map'),
            (r'\bIcons\.directions\b', 'AdaptiveIcons.directions'),
            (r'\bIcons\.timeline\b', 'AdaptiveIcons.timeline'),
            (r'\bIcons\.history\b', 'AdaptiveIcons.history'),
            (r'\bIcons\.schedule\b', 'AdaptiveIcons.schedule'),
            (r'\bIcons\.timer\b', 'AdaptiveIcons.timer'),
            (r'\bIcons\.alarm\b', 'AdaptiveIcons.alarm'),
            (r'\bIcons\.access_time\b', 'AdaptiveIcons.accessTime'),
            (r'\bIcons\.today\b', 'AdaptiveIcons.today'),
            (r'\bIcons\.event\b', 'AdaptiveIcons.event'),
            (r'\bIcons\.calendar_today\b', 'AdaptiveIcons.calendarToday'),
            (r'\bIcons\.date_range\b', 'AdaptiveIcons.dateRange'),
        ]
        
        # Required import for AdaptiveIcons
        self.adaptive_icons_import = "import 'package:dasso_reader/config/adaptive_icons.dart';"

    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp and level."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if self.verbose or level in ["ERROR", "SUCCESS"]:
            print(f"[{timestamp}] {level}: {message}")

    def create_backup(self, file_path: Path) -> bool:
        """Create backup of file before modification."""
        try:
            if not self.backup_dir.exists():
                self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            relative_path = file_path.relative_to(self.project_root)
            backup_path = self.backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(file_path, backup_path)
            self.log(f"Backup created: {backup_path}", "DEBUG")
            return True
        except Exception as e:
            self.log(f"Failed to create backup for {file_path}: {e}", "ERROR")
            return False

    def validate_syntax(self, file_path: Path) -> bool:
        """Validate Dart syntax using flutter analyze."""
        try:
            result = subprocess.run(
                ["flutter", "analyze", str(file_path)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            return result.returncode == 0
        except Exception as e:
            self.log(f"Syntax validation failed for {file_path}: {e}", "ERROR")
            return False

    def rollback_file(self, file_path: Path) -> bool:
        """Rollback file from backup if syntax validation fails."""
        try:
            relative_path = file_path.relative_to(self.project_root)
            backup_path = self.backup_dir / relative_path
            
            if backup_path.exists():
                shutil.copy2(backup_path, file_path)
                self.log(f"Rolled back: {file_path}", "WARNING")
                return True
            else:
                self.log(f"No backup found for rollback: {file_path}", "ERROR")
                return False
        except Exception as e:
            self.log(f"Rollback failed for {file_path}: {e}", "ERROR")
            return False

    def add_adaptive_icons_import(self, content: str) -> str:
        """Add AdaptiveIcons import if not present."""
        if "adaptive_icons.dart" in content:
            return content
        
        # Find the last import statement
        import_pattern = r"^import\s+['\"][^'\"]+['\"];?\s*$"
        lines = content.split('\n')
        last_import_index = -1
        
        for i, line in enumerate(lines):
            if re.match(import_pattern, line.strip()):
                last_import_index = i
        
        if last_import_index >= 0:
            lines.insert(last_import_index + 1, self.adaptive_icons_import)
            return '\n'.join(lines)
        else:
            # No imports found, add at the beginning
            return f"{self.adaptive_icons_import}\n{content}"

    def apply_icon_fixes(self, content: str) -> Tuple[str, int]:
        """Apply icon conversion patterns to content."""
        fixes_count = 0
        modified_content = content
        
        for pattern, replacement in self.icon_patterns:
            matches = re.findall(pattern, modified_content)
            if matches:
                modified_content = re.sub(pattern, replacement, modified_content)
                fixes_count += len(matches)
                self.log(f"Applied {len(matches)} fixes for pattern: {pattern}", "DEBUG")
        
        # Add import if any fixes were applied
        if fixes_count > 0:
            modified_content = self.add_adaptive_icons_import(modified_content)
        
        return modified_content, fixes_count

    def process_file(self, file_path: Path) -> bool:
        """Process a single file for icon fixes."""
        try:
            self.log(f"Processing: {file_path.relative_to(self.project_root)}")
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Apply fixes
            modified_content, fixes_count = self.apply_icon_fixes(original_content)
            
            if fixes_count == 0:
                self.log(f"No fixes needed for: {file_path.name}", "DEBUG")
                return True
            
            if self.dry_run:
                self.log(f"DRY RUN: Would apply {fixes_count} fixes to {file_path.name}")
                self.fixes_applied += fixes_count
                return True
            
            # Create backup
            if not self.create_backup(file_path):
                return False
            
            # Write modified content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            # Validate syntax
            if not self.validate_syntax(file_path):
                self.log(f"Syntax validation failed, rolling back: {file_path.name}", "WARNING")
                if not self.rollback_file(file_path):
                    self.errors_encountered += 1
                    return False
                return False
            
            self.fixes_applied += fixes_count
            self.log(f"Successfully applied {fixes_count} fixes to: {file_path.name}", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error processing {file_path}: {e}", "ERROR")
            self.errors_encountered += 1
            return False

    def find_dart_files(self) -> List[Path]:
        """Find all Dart files in lib directory."""
        lib_dir = self.project_root / "lib"
        if not lib_dir.exists():
            self.log("lib directory not found", "ERROR")
            return []
        
        dart_files = []
        for dart_file in lib_dir.rglob("*.dart"):
            # Skip generated files and test files
            if any(skip in str(dart_file) for skip in ['.g.dart', '.freezed.dart', '_test.dart', 'test/']):
                continue
            dart_files.append(dart_file)
        
        return sorted(dart_files)

    def run(self, batch_mode: bool = False) -> bool:
        """Run the icons fixer."""
        self.log("Starting Safe Icons Fixer v2.0")
        self.log(f"Project root: {self.project_root}")
        self.log(f"Dry run: {self.dry_run}")
        self.log(f"Batch mode: {batch_mode}")
        
        dart_files = self.find_dart_files()
        if not dart_files:
            self.log("No Dart files found to process", "ERROR")
            return False
        
        self.log(f"Found {len(dart_files)} Dart files to process")
        
        for i, file_path in enumerate(dart_files, 1):
            self.log(f"Progress: {i}/{len(dart_files)}")
            
            if self.process_file(file_path):
                self.files_processed += 1
            
            if not batch_mode and not self.dry_run:
                # Interactive mode - ask to continue
                response = input(f"Continue processing? (y/n/q): ").lower()
                if response == 'q':
                    break
                elif response == 'n':
                    continue
        
        # Summary
        self.log("=" * 50)
        self.log("ICONS FIXER SUMMARY")
        self.log("=" * 50)
        self.log(f"Files processed: {self.files_processed}")
        self.log(f"Fixes applied: {self.fixes_applied}")
        self.log(f"Errors encountered: {self.errors_encountered}")
        
        if not self.dry_run and self.backup_dir.exists():
            self.log(f"Backups stored in: {self.backup_dir}")
        
        return self.errors_encountered == 0

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Safe Icons Fixer v2.0 for DassoShu Reader")
    parser.add_argument("--batch", action="store_true", help="Run in batch mode without prompts")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Find project root
    current_dir = Path.cwd()
    project_root = current_dir
    
    # Look for pubspec.yaml to confirm project root
    while project_root != project_root.parent:
        if (project_root / "pubspec.yaml").exists():
            break
        project_root = project_root.parent
    else:
        print("ERROR: Could not find Flutter project root (pubspec.yaml not found)")
        sys.exit(1)
    
    fixer = SafeIconsFixer(str(project_root), dry_run=args.dry_run, verbose=args.verbose)
    success = fixer.run(batch_mode=args.batch)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
